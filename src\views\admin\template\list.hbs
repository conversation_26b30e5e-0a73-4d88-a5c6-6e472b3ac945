<div class="header-container">
  <h1>Templates</h1>
  <a href="/admin/template/create?key={{key}}" class="btn btn-blue">Create New Template</a>
</div>

<div class="table-container">
  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Template</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {{#each templates}}
      <tr>
        <td data-label="ID">{{this.id}}</td>
        <td data-label="Template">
          <div class="template-content">{{this.template}}</div>
        </td>
        <td data-label="Actions">
          <button class="btn btn-secondary edit-btn" data-id="{{this.id}}" data-template="{{this.template}}">Edit</button>
        </td>
      </tr>
      {{/each}}
    </tbody>
  </table>
</div>

<div id="editTemplateModal" class="modal">
  <div class="modal-content">
    <button class="modal-close" id="closeModal" aria-label="Close modal">
      <i class="fas fa-times"></i>
    </button>
    <div class="modal-header">
      <h2>Edit Template</h2>
    </div>
    <div class="modal-body">
      <form id="editTemplateForm">
        <input type="hidden" id="template_id" name="id">
        <div class="form-div">
          <label for="template_text">Template Content:</label>
          <textarea id="template_text" name="template" rows="10" class="form-control"></textarea>
          <small class="form-text text-muted">Use {url} or {link} as placeholders for the test URL</small>
        </div>
        <div id="formMessage" class="mt-3 text-center"></div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-gray" id="cancelModal">Cancel</button>
      <button type="submit" form="editTemplateForm" class="btn btn-blue">Save</button>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
  const modal = document.getElementById('editTemplateModal');
  const formMessage = document.getElementById('formMessage');
  const templateIdInput = document.getElementById('template_id');
  const templateTextInput = document.getElementById('template_text');



  // Edit template buttons
  document.querySelectorAll('.edit-btn').forEach(button => {
    button.addEventListener('click', () => {
      const id = button.getAttribute('data-id');
      const template = button.getAttribute('data-template');

      templateIdInput.value = id;
      templateTextInput.value = template;

      modal.classList.add('active');
      modal.style.display = 'flex';
    });
  });

  // Close modal
  document.querySelectorAll('#closeModal, #cancelModal').forEach(button => {
    button.addEventListener('click', () => {
      modal.classList.remove('active');
      modal.style.display = 'none';
    });
  });

  // Submit edit form
  document.getElementById('editTemplateForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target);

    try {
      const response = await fetch(`/admin/template/${templateIdInput.value}?key={{key}}`, {
        method: 'PATCH',
        body: new URLSearchParams(formData),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const data = await response.json();

      if (data.success) {
        formMessage.textContent = data.message;
        formMessage.style.color = 'green';
        setTimeout(() => {
          location.reload();
        }, 1500);
      } else {
        formMessage.textContent = data.error;
        formMessage.style.color = 'red';
      }
    } catch (error) {
      formMessage.textContent = `Error: ${error.message}`;
      formMessage.style.color = 'red';
    }
  });

  // Close modal when clicking outside
  window.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.remove('active');
      modal.style.display = 'none';
    }
  });
});
</script>
