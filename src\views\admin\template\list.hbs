<div class="container">
  <h1>Templates</h1>

  <div class="header-container">
    <a href="/admin/template/create?key={{key}}" class="btn btn-blue">Create New Template</a>
  </div>

  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Template</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {{#each templates}}
      <tr>
        <td>{{this.id}}</td>
        <td>
          <div class="template-content">{{this.template}}</div>
        </td>
        <td>
          <button class="edit-btn" data-id="{{this.id}}" data-template="{{this.template}}">Edit</button>
        </td>
      </tr>
      {{/each}}
    </tbody>
  </table>
</div>

<div id="editTemplateModal" class="modal">
  <div class="modal-content">
    <h2>Edit Template</h2>
    <form id="editTemplateForm">
      <input type="hidden" id="template_id" name="id">
      <div class="form-div">
        <label for="template_text">Template Content:</label>
        <textarea id="template_text" name="template" rows="10" class="form-control"></textarea>
        <small>Use {url} or {link} as placeholders for the test URL</small>
      </div>
      <div style="text-align: center; margin-top: 10px;">
        <button type="submit" class="btn btn-blue">Save</button>
        <button type="button" id="closeModal" class="btn btn-gray">Cancel</button>
      </div>
    </form>
    <div id="formMessage" style="margin-top: 10px; text-align: center;"></div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
  const modal = document.getElementById('editTemplateModal');
  const formMessage = document.getElementById('formMessage');
  const templateIdInput = document.getElementById('template_id');
  const templateTextInput = document.getElementById('template_text');



  // Edit template buttons
  document.querySelectorAll('.edit-btn').forEach(button => {
    button.addEventListener('click', () => {
      const id = button.getAttribute('data-id');
      const template = button.getAttribute('data-template');

      templateIdInput.value = id;
      templateTextInput.value = template;

      modal.style.display = 'block';
    });
  });

  // Close modal
  document.getElementById('closeModal').addEventListener('click', () => {
    modal.style.display = 'none';
  });

  // Submit edit form
  document.getElementById('editTemplateForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(e.target);

    try {
      const response = await fetch(`/admin/template/${templateIdInput.value}?key={{key}}`, {
        method: 'PATCH',
        body: new URLSearchParams(formData),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const data = await response.json();

      if (data.success) {
        formMessage.textContent = data.message;
        formMessage.style.color = 'green';
        setTimeout(() => {
          location.reload();
        }, 1500);
      } else {
        formMessage.textContent = data.error;
        formMessage.style.color = 'red';
      }
    } catch (error) {
      formMessage.textContent = `Error: ${error.message}`;
      formMessage.style.color = 'red';
    }
  });

  // Close modal when clicking outside
  window.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });
});
</script>

<style>
.template-content {
  max-width: 500px;
  white-space: pre-wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 100px;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
}

.btn-gray {
  background-color: #6c757d;
  color: white;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
