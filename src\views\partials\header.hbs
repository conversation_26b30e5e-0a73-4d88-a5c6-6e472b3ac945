<header class="header">
    <h1>{{title}}</h1>
    <button class="mobile-nav-toggle" id="mobileNavToggle" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
    </button>
    <nav class="nav" id="mainNav">
            {{#if url}}
                {{#if (startsWith url '/admin')}}
                    <a href="/admin/group/list?key={{key}}">Groups</a>
                    <a href="/admin/test/create?key={{key}}">Create Test</a>
                    <a href="/admin/template/list?key={{key}}">Templates</a>
                {{/if}}
            {{else}}
                <!-- Admin links when URL is not available -->
                <a href="/admin/group/list?key={{key}}">Groups</a>
                <a href="/admin/test/create?key={{key}}">Create Test</a>
                <a href="/admin/template/list?key={{key}}">Templates</a>
            {{/if}}
    </nav>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileNavToggle = document.getElementById('mobileNavToggle');
    const mainNav = document.getElementById('mainNav');

    if (mobileNavToggle && mainNav) {
        mobileNavToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            const icon = this.querySelector('i');
            if (mainNav.classList.contains('active')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });

        // Close mobile nav when clicking on a link
        mainNav.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                mainNav.classList.remove('active');
                const icon = mobileNavToggle.querySelector('i');
                icon.className = 'fas fa-bars';
            }
        });

        // Close mobile nav when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileNavToggle.contains(e.target) && !mainNav.contains(e.target)) {
                mainNav.classList.remove('active');
                const icon = mobileNavToggle.querySelector('i');
                icon.className = 'fas fa-bars';
            }
        });
    }
});
</script>