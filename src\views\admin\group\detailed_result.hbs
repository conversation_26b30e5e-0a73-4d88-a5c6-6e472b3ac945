<h1>{{title}}</h1>

<style>
  .profile-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
    max-width: 100vw;
  }
  .profile-item {
    text-align: center;
    flex: 1 1 calc(20% - 10px);
    margin-bottom: 10px;
  }
  .export-icons {
    display: inline-block;
    margin-left: 10px;
  }
  .export-icon {
    cursor: pointer;
    margin: 0 5px;
    color: #007bff;
  }
  .export-icon:hover {
    color: #0056b3;
  }
  .snackbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px;
    border-radius: 4px;
    z-index: 1000;
  }
  
  @media screen and (max-width: 600px) {
    .profile-info {
      flex-direction: column;
    }
    .profile-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 0 10px;
      box-sizing: border-box;
    }
  }
</style>

{{#each detailedResults}}
  <div class="profile-info">
    <div class="profile-item">
      <div>Last Answer Date</div> 
      <div>{{formatDate this.profile.lastAnswerDate}}</div>
    </div>
    <div class="profile-item">
      <div>Link ID</div> 
      <a href ="/admin/profile/view?id={{this.profile.linkId}}&key={{../key}}">{{this.profile.linkId}}</a>
      <span class="export-icons">
        <i class="fas fa-clipboard export-icon" title="Copy JSON" data-user="{{this.profile.linkId}}" data-test="{{#if ../testId}}{{../testId}}{{else if this.answers.[0]}}{{this.answers.[0].testId}}{{/if}}" data-test-names="{{this.testNames}}"></i>
        <i class="fas fa-download export-icon" title="Download JSON" data-user="{{this.profile.linkId}}" data-test="{{#if ../testId}}{{../testId}}{{else if this.answers.[0]}}{{this.answers.[0].testId}}{{/if}}" data-test-names="{{this.testNames}}"></i>
      </span>
    </div>
    <div class="profile-item">
      <div>Name</div> 
      <div>
        {{#if this.profile.url}}
          <a href="https://www.upwork.com/freelancers/{{this.profile.url}}" target="_blank">{{this.profile.name}}</a>
        {{else}}
          {{this.profile.name}}
        {{/if}}
      </div>
    </div>
    <div class="profile-item">
      <div>Country</div> 
      <div>{{this.profile.country}}</div>
    </div>
    <div class="profile-item">
      <div>Hourly Rate</div> 
      <div>{{this.profile.hourlyRate}}</div>
    </div>
  </div>

  <table style="margin-bottom: 50px;">
    <thead>
      <tr>
        <th>S. No.</th>
        <th>Test ID</th>
        <th>Question</th>
        <th>Answer</th>
        <th>Time</th>
        <th>Inactive</th>
        <th>CopyPaste</th>
      </tr>
    </thead>
    <tbody>
      {{#each this.answers}}
        <tr>
          <td data-label="S. No.">{{add @index 1}}</td>
          <td data-label="Test ID"><a href="/admin/group/detailed_result?group_id={{../../groupId}}&test={{this.testId}}&key={{../../key}}">{{this.testId}}</a></td>
          <td data-label="Question">{{{this.question}}}</td>
          <td data-label="Answer" {{#if this.isCorrect}}style="background-color: #e6ffe6;"{{/if}}>{{{this.answer}}}</td>
          <td data-label="Time">{{this.timeTaken}}</td>
          <td data-label="Inactive">{{this.inactive}}</td>
          <td data-label="CopyPaste">{{this.copyPaste}}</td>
        </tr>
      {{/each}}
    </tbody>
  </table>
{{/each}}

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Copy JSON
    document.querySelectorAll('.fa-clipboard').forEach(icon => {
      icon.addEventListener('click', async (e) => {
        const userId = e.target.dataset.user;
        const testId = e.target.dataset.test;
        const testNames = e.target.dataset.testNames || '';
        const key = '{{key}}';
        console.log('Copy - userId:', userId, 'testId:', testId, 'testNames:', testNames);

        console.log('Copy - userId:', userId, 'testId:', testId, 'testNames:', testNames);

        if (!testId) {
          alert('No test ID available for export');
          return;
        }

        try {
          // Determine if it's a math or JS test based on the test names
          const endpoint = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript')
            ? '/admin/test/export/js-json'
            : '/admin/test/export/math-json';

          const response = await fetch(`${endpoint}?user_id=${userId}&test_id=${testId}&key=${key}`);
          const json = await response.json();

          await navigator.clipboard.writeText(JSON.stringify(json, null, 2));

          // Show snackbar
          const snackbar = document.createElement('div');
          snackbar.className = 'snackbar';
          snackbar.textContent = 'JSON copied to clipboard';
          document.body.appendChild(snackbar);

          setTimeout(() => snackbar.remove(), 3000);
        } catch (error) {
          alert('Error copying JSON: ' + error.message);
        }
      });
    });
    
    // Download JSON
    document.querySelectorAll('.fa-download').forEach(icon => {
      icon.addEventListener('click', async (e) => {
        const userId = e.target.dataset.user;
        const testId = e.target.dataset.test;
        const testNames = e.target.dataset.testNames || '';
        const key = '{{key}}';

        console.log('Download - userId:', userId, 'testId:', testId, 'testNames:', testNames);

        if (!testId) {
          alert('No test ID available for export');
          return;
        }

        try {
          // Determine if it's a math or JS test based on the test names
          const endpoint = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript')
            ? '/admin/test/export/js-json'
            : '/admin/test/export/math-json';
          const filePrefix = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript') ? 'js' : 'math';

          const response = await fetch(`${endpoint}?user_id=${userId}&test_id=${testId}&key=${key}`);
          const json = await response.json();

          const blob = new Blob([JSON.stringify(json, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${filePrefix}-test-${userId}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } catch (error) {
          alert('Error downloading JSON: ' + error.message);
        }
      });
    });
  });
</script>
