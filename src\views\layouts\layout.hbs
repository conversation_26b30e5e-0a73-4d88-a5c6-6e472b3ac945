<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0 auto;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            min-height: 100vh;
            max-width: 100vw;
            display: flex;
            flex-direction: column;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background-color: #fff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: auto;
            margin-top: 20px;
        }

        th, td {
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9em;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:nth-child(even) {
            background-color: #f9fafc;
        }

        tr:hover {
            background-color: #f0f4f8;
            transition: background-color 0.3s ease;
        }

        td img {
            max-width: 500px;
            max-height: 300px;
            display: block;
        }

        @media screen and (max-width: 600px) {
            table, thead, tbody, th, td, tr {
                display: block;
                max-width: 100vw;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                border: 1px solid #ccc;
                margin-bottom: 10px;
                border-radius: 8px;
                overflow: hidden;
                display: flex;
                flex-wrap: wrap;
            }

            td {
                border: none;
                position: relative;
                padding-left: 50%;
                text-align: right;
                flex-basis: 100%;
                box-sizing: border-box;
                word-break: break-word;
            }

            td:before {
                content: attr(data-label);
                position: absolute;
                left: 6px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: left;
                font-weight: bold;
            }

            td[data-label="Countries"], td[data-label="Test IDs"], td[data-label="Question"] {
                flex-basis: 100%;
                text-align: left;
                padding-left: 6px;
            }

            td[data-label="Countries"]:before, td[data-label="Test IDs"]:before, td[data-label="Question"]:before {
                position: static;
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }

            td img {
                max-width: 100%;
            }
        }

        .btn {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #45a049;
        }

        .btn-blue {
            background-color: #3b82f6;
        }

        .btn-blue:hover {
            background-color: #2563eb;
        }

        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #45a049;
        }

        .content-wrapper {
            flex: 1;
            padding: 0 20px;
            overflow-y: auto;
        }

        /* Export icons styling */
        .export-icons {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .export-icon {
            cursor: pointer;
            font-size: 1.2rem;
            color: #3b82f6;
            transition: color 0.2s;
        }

        .export-icon:hover {
            color: #1d4ed8;
        }

        .snackbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }

        @keyframes fadeIn {
            from { opacity: 0; bottom: 0; }
            to { opacity: 1; bottom: 20px; }
        }

        @keyframes fadeOut {
            from { opacity: 1; bottom: 20px; }
            to { opacity: 0; bottom: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        {{> header}}
        <div class="content-wrapper">
            {{{body}}}
        </div>
        {{> footer}}
    </div>
</body>
</html>