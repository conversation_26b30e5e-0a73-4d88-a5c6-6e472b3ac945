document.addEventListener('DOMContentLoaded', function() {
  const openModalBtn = document.getElementById('openModalBtn');
  const modal = document.getElementById('createGroupModal');

  if (openModalBtn && modal) {
    openModalBtn.addEventListener('click', function() {
      modal.classList.add('active');
      modal.style.display = 'flex';
    });

    window.addEventListener('click', function(event) {
      if (event.target === modal) {
        modal.classList.remove('active');
        modal.style.display = 'none';
      }
    });

    // Handle close buttons
    const closeButtons = modal.querySelectorAll('.modal-close, [onclick*="style.display=\'none\'"]');
    closeButtons.forEach(button => {
      button.addEventListener('click', function() {
        modal.classList.remove('active');
        modal.style.display = 'none';
      });
    });
  }
});