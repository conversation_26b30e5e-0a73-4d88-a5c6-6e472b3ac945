<div class="header-container">
  <h1>Test Results</h1>
  {{#if testResults.length}}
    <div style="display: flex; justify-content: space-between; align-items: center; gap: 1rem;">
      <a href ="/admin/group/detailed_result?group_id={{groupId}}&key={{this.key}}">Detailed Result</a>
      <button id="copyButton" class="btn btn-blue">Copy Selected Rows</button>
    </div>
  {{/if}}
</div>

<style>
  .export-icons {
    display: inline-block;
    margin-left: 10px;
  }
  .export-icon {
    cursor: pointer;
    margin: 0 5px;
    color: #007bff;
  }
  .export-icon:hover {
    color: #0056b3;
  }

  .snackbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px;
    border-radius: 4px;
    z-index: 1000;
  }
</style>


{{#if testResults.length}}
  <table id="resultsTable">
    <thead>
      <tr>
        <th>User ID</th>
        <th>User Name</th>
        <th>Country</th>
        <th>
          Pass
          {{#if (or (eq currentSort "pass") (eq currentSort "pass,date") (eq currentSort "date,pass"))}}
            <span class="ml-1">▼</span>
          {{/if}}
        </th>
        <th class="px-4 py-2">
          Date
          {{#if (or (eq currentSort "date") (eq currentSort "date,pass") (eq currentSort "pass,date"))}}
            <span class="ml-1">▼</span>
          {{/if}}
        </th>
        <th> Question Details</th>
        <th><input type="checkbox" id="selectAll"></th>
      </tr>
    </thead>
    <tbody>
      {{#each testResults}}
        <tr>
          <td data-label="User ID"><a href="/admin/profile/view?id={{this.user_link_id}}&key={{../key}}">{{this.user_link_id}}</a>
            <span class="export-icons">
              {{#if this.user_link_id}}
                <i class="fas fa-clipboard export-icon" title="Copy JSON" data-user="{{this.user_link_id}}" data-test="{{#if ../testId}}{{../testId}}{{else if this.question_details.[0]}}{{this.question_details.[0].test_id}}{{/if}}" data-test-names="{{this.test_names}}"></i>
                <i class="fas fa-download export-icon" title="Download JSON" data-user="{{this.user_link_id}}" data-test="{{#if ../testId}}{{../testId}}{{else if this.question_details.[0]}}{{this.question_details.[0].test_id}}{{/if}}" data-test-names="{{this.test_names}}"></i>
              {{else}}
                <span title="User ID is missing, cannot export data">
                  <i class="fas fa-clipboard export-icon disabled" style="color: #ccc; cursor: not-allowed;"></i>
                  <i class="fas fa-download export-icon disabled" style="color: #ccc; cursor: not-allowed;"></i>
                </span>
              {{/if}}
            </span>
          </td>
          <td data-label="User Name">{{this.user_name}}</td>
          <td data-label="Country">{{#if this.country}}{{this.country}}{{else}}-{{/if}}</td>
          <td data-label="Score">{{this.correct_answers}} / {{this.total_answers}}</td>
          <td data-label="Date">{{formatDate this.completion_date}}</td>
          <td data-label="Question Details" data-question-details='{{stringifyJson this.question_details}}'>
            {{#each this.question_details}}
              <span style="{{#if this.isCorrect}}background-color: #90EE90;{{/if}}">
                {{this.answer_string}}
              </span>
              {{#unless @last}}<span>---</span>{{/unless}}
            {{/each}}
          </td>
          <td>
            <input type="checkbox" class="row-selector" data-row-id="{{@index}}">
          </td>
        </tr>
      {{/each}}
    </tbody>
  </table>
{{else}}
  <p style="color: #666;">No test results found for this group.</p>
{{/if}}

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const copyButton = document.getElementById('copyButton');
    const table = document.getElementById('resultsTable');
    const selectAllCheckbox = document.getElementById('selectAll');
    const rowSelectors = document.querySelectorAll('.row-selector');

    selectAllCheckbox.addEventListener('change', function() {
      rowSelectors.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
    });

    rowSelectors.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        selectAllCheckbox.checked = Array.from(rowSelectors).every(cb => cb.checked);
      });
    });

    copyButton.addEventListener('click', function() {
      const selectedRows = document.querySelectorAll('.row-selector:checked');
      if (selectedRows.length === 0) {
        alert('Please select at least one row to copy.');
        return;
      }

      let copyText = '';
      // const headers = Array.from(table.querySelectorAll('thead th')).slice(0, -1).map(th => th.textContent.trim());
      // copyText += headers.join('\t') + '\n';

      selectedRows.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const cells = Array.from(row.querySelectorAll('td')).slice(0, -1);
        const rowData = cells.map(cell => {
          let text = cell.textContent.trim().replace(/\n/g, ' ');
          if (cell.dataset.label === "Question Details") {
            const details = JSON.parse(cell.getAttribute('data-question-details'));
            text = details.map(detail => detail.answer_string).join("---");
          }
          return text;
        });
        copyText += rowData.join('\t') + '\n';
      });

      navigator.clipboard.writeText(copyText).then(() => {
        copyButton.style.backgroundColor = '#4CAF50';
        copyButton.textContent = 'Copied!';
        setTimeout(() => {
          copyButton.style.backgroundColor = '#3b82f6';
          copyButton.textContent = 'Copy Selected Rows';
        }, 2000);
      }).catch(err => {
        console.error('Failed to copy: ', err);
        copyButton.style.backgroundColor = '#ef4444';
        copyButton.textContent = 'Failed to copy';
        setTimeout(() => {
          copyButton.style.backgroundColor = '#3b82f6';
          copyButton.textContent = 'Copy Selected Rows';
        }, 2000);
      });
    });

    document.querySelectorAll('.fa-clipboard').forEach(icon => {
      icon.addEventListener('click', async (e) => {
        const userId = e.target.dataset.user;
        const testId = e.target.dataset.test;
        const testNames = e.target.dataset.testNames || '';
        // Get the key from URL if available, otherwise use the template variable
        const urlParams = new URLSearchParams(window.location.search);
        const key = urlParams.get('key') || '{{key}}';

        console.log('Copy - userId:', userId, 'testId:', testId, 'testNames:', testNames);

        // Check if userId is valid
        if (!userId) {
          alert('Error: User ID is missing. Cannot export data.');
          return;
        }

        if (!testId) {
          alert('No test ID available for export');
          return;
        }

        try {
          // Determine if it's a math or JS test based on the test names
          const endpoint = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript')
            ? '/admin/test/export/js-json'
            : '/admin/test/export/math-json';

          const response = await fetch(`${endpoint}?user_id=${userId}&test_id=${testId}&key=${key}`);
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          const json = await response.json();

          await navigator.clipboard.writeText(JSON.stringify(json, null, 2));

          // Show snackbar
          const snackbar = document.createElement('div');
          snackbar.className = 'snackbar';
          snackbar.textContent = 'JSON copied to clipboard';
          document.body.appendChild(snackbar);

          setTimeout(() => snackbar.remove(), 3000);
        } catch (error) {
          alert('Error copying JSON: ' + error.message);
        }
      });
    });

    // Download JSON
    document.querySelectorAll('.fa-download').forEach(icon => {
      icon.addEventListener('click', async (e) => {
        const userId = e.target.dataset.user;
        const testId = e.target.dataset.test;
        const testNames = e.target.dataset.testNames || '';
        // Get the key from URL if available, otherwise use the template variable
        const urlParams = new URLSearchParams(window.location.search);
        const key = urlParams.get('key') || '{{key}}';

        console.log('Download - userId:', userId, 'testId:', testId, 'testNames:', testNames);

        // Check if userId is valid
        if (!userId) {
          alert('Error: User ID is missing. Cannot export data.');
          return;
        }

        if (!testId) {
          alert('No test ID available for export');
          return;
        }

        try {
          // Determine if it's a math or JS test based on the test names
          const endpoint = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript')
            ? '/admin/test/export/js-json'
            : '/admin/test/export/math-json';
          const filePrefix = testNames.toLowerCase().includes('js') || testNames.toLowerCase().includes('javascript') ? 'js' : 'math';

          const response = await fetch(`${endpoint}?user_id=${userId}&test_id=${testId}&key=${key}`);
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          const json = await response.json();

          const blob = new Blob([JSON.stringify(json, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${filePrefix}-test-${userId}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } catch (error) {
          alert('Error downloading JSON: ' + error.message);
        }
      });
    });
  });
</script>

<style>
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .export-icons {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    gap: 8px;
  }

  .export-icon {
    cursor: pointer;
    color: #007bff;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
  }

  .export-icon:hover {
    color: #0056b3;
  }

  .export-icon.disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
  }

  .snackbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px;
    border-radius: 4px;
    z-index: 1000;
  }
</style>