<div class="flex-container">
    <h1>{{test.name}}</h1>
    <div class="question-container">
      <h2>
        <div id="question-div">
          {{{question}}}
        </div>
      </h2>
      <form id="answerForm" method="POST" action="/test/attend?user={{userLinkId}}&test={{test_id}}">
        <input type="hidden" name="answer_type" id="answer_type" value="{{answer_type}}">
        <input type="hidden" name="test_id" id="test_id" value="{{test_id}}">
        <input type="hidden" name="question_id" id="question_id" value="{{question_id}}">
        <input type="hidden" name="user_id" id="user_id" value="{{user_id}}">
        <input type="hidden" name="result_salt" id="result_salt" value="{{result_salt}}">
        {{{answer_html}}}
        <div class="submit-container">
          <button type="submit" id="submitButton" class="btn btn-blue">
            Submit Answer
          </button>
        </div>
      </form>
    </div>
</div>

<!-- Styles are now in main.css -->
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
  }

  #submitButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  @media (min-width: 768px) {
    .flex-container {
      flex-direction: row;
      justify-content: center;
    }

    .content-left, .content-right {
      width: 50%;
    }
  }
</style>

<script>
  window.trackingConfig = {{{json tracking_config}}};
</script>
<script src="/js/attend.js"></script>
